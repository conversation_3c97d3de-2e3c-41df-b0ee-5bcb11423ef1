import pandas as pd
import os
import glob
import re

def extract_segment_from_path(path):
    """
    Extract conference segment name from path.

    Args:
        path (str): The file path to extract segment from

    Returns:
        str or None: The extracted segment name or None if not found
    """
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)
    if match:
        segment = match.group(1)
        print(f"Found segment: {segment}")
        return segment
    else:
        print(f"Desired segment not found in path: {path}")
        # Prompt for manual input
        segment = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
        if segment.strip():
            print(f"Using manually entered segment: {segment}")
            return segment
        else:
            print("No segment name provided")
            return None

def extract_sheets_to_csv(xlsx_filepath, output_dir, segment=None):
    """
    Extracts all sheets from an XLSX file and saves each sheet as a separate CSV file.

    Args:
        xlsx_filepath: The path to the XLSX file.
        output_dir: The directory where the CSV files will be saved.
        segment: The conference segment name to include in output filenames.
    """

    try:
        # Check if the output directory exists, if not create it
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Read the Excel file (reading all sheets at once is generally more efficient)
        xls = pd.ExcelFile(xlsx_filepath)  # More efficient for multiple sheets

        for sheet_name in xls.sheet_names:
            try:
                df = pd.read_excel(xls, sheet_name=sheet_name)

                # Construct the CSV file path with segment name if available
                if segment:
                    csv_filename = f"{segment}_{sheet_name}.csv"
                else:
                    csv_filename = f"{sheet_name}.csv"

                csv_filepath = os.path.join(output_dir, csv_filename)

                # Save the DataFrame to a CSV file
                df.to_csv(csv_filepath, index=False, encoding='utf-8-sig')  # UTF-8-sig encoding as per user preference

                print(f"Sheet '{sheet_name}' saved to '{csv_filepath}'")

            except Exception as e:
                print(f"Error processing sheet '{sheet_name}': {e}")


    except FileNotFoundError:
        print(f"Error: XLSX file not found at '{xlsx_filepath}'")
    except Exception as e:
        print(f"An error occurred: {e}")

# Get the file path from user input
path = input('Enter file path: ')

# Ensure the path ends with a backslash if it doesn't already
if not path.endswith('\\'):
    path += '\\'

# Extract segment from path
segment = extract_segment_from_path(path)

# Use glob to find all .xlsx files in the specified path
xlsx_files = glob.glob(path + '*.xlsx')

# Create the output directory if it doesn't exist
os.makedirs("sheets", exist_ok=True)
output_directory = path + "sheets"

# Loop through each .xlsx file found and process it
for xlsx_file in xlsx_files:
    print(f"\nProcessing file: {os.path.basename(xlsx_file)}")
    extract_sheets_to_csv(xlsx_file, output_directory, segment)

print("Extraction complete.")
