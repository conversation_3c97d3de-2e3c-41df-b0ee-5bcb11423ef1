import os
from selenium import webdriver
from selenium.webdriver.common.by import By
import time
timestr = time.strftime("%d%m%Y")
options = webdriver.ChromeOptions()
options.add_argument('--headless')
options.add_experimental_option("excludeSwitches", ["enable-logging"])
prefs = {
"upload.default_directory": r"H:\Master Bounces and Unsubs\Replied Ext\Prev_Rep_bounces_csv",
"upload.prompt_for_download": False,
"upload.directory_upgrade": True
}
options.add_experimental_option('prefs', prefs)
driver = webdriver.Chrome(options)
driver.set_window_size(1920, 1080)  # Ensure proper rendering
driver.get("http://swa.dbms.org.in/")
driver.find_element(By.ID, "username").send_keys("<EMAIL>")
driver.find_element(By.ID, "password").send_keys("Magnus@123")
time.sleep(3)
driver.find_element(By.ID, "btnSubmit").click()
time.sleep(3)
driver.get("http://swa.dbms.org.in/data_filter_sets")
driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=318&datafiltername=MGConferences_unsubscribers")
button = driver.find_element(By.ID, 'up_file')

# Define the file path for MGConferences_unsubscribers
file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\new_records\PP_Global_Unsubscribers.csv")

# Direct file upload using send_keys
button.send_keys(file_path)
driver.find_element(By.ID, "duplicates").click()
time.sleep(3)
driver.find_element(By.ID, "submit_key").click()
time.sleep(10)
print('[MGConferences_unsubscribers] Upload Completed!')

driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=182&datafiltername=Hard_bouncers")
button = driver.find_element(By.ID, 'up_file')

# Define the file path for Hard_bouncers
file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\new_records\Master_Hardbounces.csv")

# Direct file upload using send_keys
button.send_keys(file_path)
driver.find_element(By.ID, "duplicates").click()
time.sleep(3)
driver.find_element(By.ID, "submit_key").click()
time.sleep(10)
print('[Hard_bouncers] Upload Completed!')

driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=318&datafiltername=MGConferences_unsubscribers")
button = driver.find_element(By.ID, 'up_file')

# Define the file path for MGConferences_unsubscribers (second upload)
file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\new_records\Master_Unsubscribes.csv")

# Direct file upload using send_keys
button.send_keys(file_path)
driver.find_element(By.ID, "duplicates").click()
time.sleep(3)
driver.find_element(By.ID, "submit_key").click()
time.sleep(10)
print('[MGConferences_unsubscribers] Upload Completed!')

driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=318&datafiltername=MGConferences_unsubscribers")
button = driver.find_element(By.ID, 'up_file')

# Define the file path for MGConferences_unsubscribers (third upload)
file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Bounces\ee.exe\ee_unsubs\merged\Conferences_unsubscribes.csv")

# Direct file upload using send_keys
button.send_keys(file_path)
driver.find_element(By.ID, "duplicates").click()
time.sleep(3)
driver.find_element(By.ID, "submit_key").click()
time.sleep(10)
print('[MGConferences_unsubscribers] Upload Completed!')

driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=18&datafiltername=MMCJournals_unsubscribers")
button = driver.find_element(By.ID, 'up_file')

# Define the file path for MMCJournals_unsubscribers
file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Bounces\ee.exe\ee_j.exe\ee_unsubs\merged\Journals_unsubscribes.csv")

# Direct file upload using send_keys
button.send_keys(file_path)
driver.find_element(By.ID, "duplicates").click()
time.sleep(3)
driver.find_element(By.ID, "submit_key").click()
time.sleep(10)
print('[MMCJournals_unsubscribers] Upload Completed!')

driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=14&datafiltername=MTJournals_unsubscribers")
button = driver.find_element(By.ID, 'up_file')

# Define the file path for MTJournals_unsubscribers
file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Bounces\ee.exe\ee_j.exe\ee_unsubs\merged\Journals_unsubscribes.csv")

# Direct file upload using send_keys
button.send_keys(file_path)
driver.find_element(By.ID, "duplicates").click()
time.sleep(3)
driver.find_element(By.ID, "submit_key").click()
time.sleep(10)
print('[MTJournals_unsubscribers] Upload Completed!')
