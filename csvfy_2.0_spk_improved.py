#!/usr/bin/env python3
"""
CSV Processing Script for Conference Data (Modified - No Hardbounces/Replied Bounces Filtering)

This script processes CSV files for conference data, extracting conference segment names,
filtering data, and preparing output files for further processing.
Note: Hardbounces and replied bounces filtering has been commented out.
"""

import os
import glob
import pandas as pd
import numpy as np
import re
import random
import warnings
import rich_progress

# Suppress SettingWithCopyWarning
from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)


def create_gradient_bar(total, desc="Processing"):
    """
    Create a Rich progress bar with gradient colors.

    Args:
        total: Total number of items
        desc: Description for the progress bar

    Returns:
        Rich progress bar instance and update function
    """
    # Define color schemes based on the description
    color_schemes = ["blue", "green", "purple", "orange"]

    # Choose a color scheme based on the process type to ensure consistency within a process
    # but variety between different processes
    color_seed = sum(ord(c) for c in desc) % len(color_schemes)
    color_scheme = color_schemes[color_seed]

    # Create and return the progress bar with the rich_progress module
    progress, update_func = rich_progress.create_progress_bar(total, desc, color_scheme)

    return progress, update_func


def print_status(message, style="info"):
    """
    Print a status message with appropriate styling using rich_progress.

    Args:
        message: The message to print
        style: The style of the message ("info", "success", "error", "warning", "header")
    """
    rich_progress.print_status(message, style)

# Base paths
MASTER_DIR = r"H:/Master Bounces and Unsubs"
POSTPANEL_DIR = os.path.join(MASTER_DIR, "Postpanel Unsubs")
MASTER_BOUNCES_DIR = os.path.join(MASTER_DIR, "Master Bounces and Unsubs")
REPLIED_DIR = os.path.join(MASTER_DIR, "Replied Ext/Prev_Rep_bounces_csv")


def extract_conference_name(path: str) -> str:
    """
    Extract conference segment name from a path using regex.

    Args:
        path: Path string to extract from

    Returns:
        Conference name or prompts for manual input if not found
    """
    # Define a regex pattern to match the desired segment, including spaces
    pattern = r"\\([\w\s-]+\d{4})\\?"

    # Search for the pattern in the path
    match = re.search(pattern, path)

    if match:
        csn = match.group(1)
        print_status(f"Conference Segment Name (CSN): {csn}", "success")
        return csn
    else:
        print_status("Desired segment not found in path.", "warning")

        # Prompt for manual input
        csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ").strip()

        if csn:
            print_status(f"Using manually entered segment: {csn}", "info")
            return csn
        else:
            print_status("No segment name provided.", "error")
            raise ValueError("No conference segment name provided")


def process_mw_unsubs() -> None:
    """Process MailWizz unsubscribers and save to CSV."""
    print("Processing MailWizz unsubscribers...")

    # Process MW unsubscribers
    os.chdir(os.path.join(POSTPANEL_DIR, "mw"))

    # List all CSV files and concatenate them
    csv_files = glob.glob('*.csv')
    if not csv_files:
        print("No CSV files found in MW directory")
        return

    try:
        df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip',
                                          usecols=['email', 'status', 'date_added'])
                              for f in csv_files], ignore_index=True)

        # Filter for unsubscribed status
        status = ["unsubscribed"]
        unsub_df = df_concat[df_concat['status'].isin(status)]

        # Remove duplicates
        unsub_df1 = unsub_df.drop_duplicates(subset='email')

        # Save intermediate file
        unsub_df1.to_csv('mw_list_unsubscriber.csv', encoding='utf-8-sig', index=False)

        # Read back and rename columns
        unsub_df2 = pd.read_csv('mw_list_unsubscriber.csv')
        unsub_df2.rename(columns={'email': 'Email', 'status': 'Conference Name',
                                 'date_added': 'DateTime Info'}, inplace=True)

        # Replace values
        unsub_dfg = unsub_df2.apply(lambda x: x.str.replace('unsubscribed', 'Global Unsubscriber'))

        # Save to Postpanel Unsubs directory
        os.chdir(POSTPANEL_DIR)
        unsub_dfg.to_csv('mw_unsubscribers.csv', index=False)
        print("MailWizz unsubscribers processed successfully")

    except Exception as e:
        print(f"Error processing MW unsubscribers: {str(e)}")


def process_port1_unsubs() -> None:
    """Process Port1 unsubscribers and save to CSV."""
    print("Processing Port1 unsubscribers...")

    try:
        os.chdir(os.path.join(POSTPANEL_DIR, "port1"))

        # List all CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            print("No CSV files found in Port1 directory")
            return

        # Concatenate all CSV files
        unsub_mgport = pd.concat([pd.read_csv(f, on_bad_lines='skip', encoding='latin')
                                 for f in csv_files], ignore_index=True)

        # Rename columns
        unsub_mgport.rename(columns={'Email ID': 'Email', 'To': 'Conference Name',
                                    'Date Info': 'DateTime Info'}, inplace=True)

        # Replace values using regex
        unsub_mgportg = unsub_mgport.replace(to_replace=r".*\(.+?\)", value='Global Unsubscriber', regex=True)
        unsub_mgportg['Conference Name'] = unsub_mgportg['Conference Name'].fillna('Global Unsubscriber')

        # Remove duplicates
        unsub_mgportg.drop_duplicates(subset='Email', inplace=True)

        # Save to Postpanel Unsubs directory
        os.chdir(POSTPANEL_DIR)
        unsub_mgportg.to_csv('unsubcriber_sheet.csv', mode='w+', index=False)
        print("Port1 unsubscribers processed successfully")

    except Exception as e:
        print(f"Error processing Port1 unsubscribers: {str(e)}")


def process_global_unsubs(sp_filter: str) -> None:
    """
    Process global unsubscribers and save to CSV.

    Args:
        sp_filter: Conference segment name for filtering
    """
    print("Processing global unsubscribers...")

    try:
        os.chdir(POSTPANEL_DIR)

        # List all CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            print("No CSV files found in Postpanel Unsubs directory")
            return

        # Concatenate all CSV files
        glob_unsubs = pd.concat([pd.read_csv(f) for f in csv_files], ignore_index=True)

        # Clean data
        glob_unsubs = glob_unsubs.apply(lambda x: x.str.replace(' - ', '-') if isinstance(x, str) else x)

        # Drop DateTime Info column
        if 'DateTime Info' in glob_unsubs.columns:
            glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)

        # Filter by conference name
        options = ['Global Unsubscriber', sp_filter]
        unsub_df3 = glob_unsubs[glob_unsubs['Conference Name'].isin(options)]

        # Remove duplicates and convert emails to lowercase
        unsub_df3.drop_duplicates(subset='Email', inplace=True, ignore_index=True)
        unsub_df3.drop(['Conference Name'], axis=1, inplace=True)
        unsub_df3[['Email']] = unsub_df3[['Email']].applymap(lambda x: x.lower() if isinstance(x, str) else x)

        # Save to Master Bounces and Unsubs directory
        output_path = os.path.join(MASTER_BOUNCES_DIR, "PP_Global_Unsubscribers.csv")
        unsub_df3.to_csv(output_path, index=False)
        print(f"Global unsubscribers saved to {output_path}")

    except Exception as e:
        print(f"Error processing global unsubscribers: {str(e)}")


def clean_name(name: str) -> str:
    """
    Clean name by removing text after the first comma.
    
    Args:
        name: Name string to clean
        
    Returns:
        Cleaned name string
    """
    return str(name).split(',')[0].strip()


def process_conference_data(path: str, csn: str) -> pd.DataFrame:
    """
    Process conference data from CSV files.

    Args:
        path: Path to the directory containing CSV files
        csn: Conference segment name

    Returns:
        Processed DataFrame
    """
    print(f"Processing conference data for {csn}...")

    # Create a progress bar for the overall process
    process_steps = 5  # Number of major processing steps
    process_bar, update_process = create_gradient_bar(total=process_steps, desc="Processing data")

    try:
        os.chdir(path)

        # Create process directory
        process_dir = os.path.join(path, "process")
        os.makedirs(process_dir, exist_ok=True)
        update_process(1, "Created process directory")  # Step 1 complete

        # List all CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            print(f"No CSV files found in {path}")
            process_bar.stop()
            return pd.DataFrame()

        # Concatenate all CSV files
        df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', low_memory=False)
                              for f in csv_files], ignore_index=True)

        # Standardize column names
        df_concat.rename(columns={'Author Name': 'Name', 'Name ': 'Name'}, inplace=True)

        # Clean names by removing text after comma
        df_concat['Name'] = df_concat['Name'].apply(clean_name)

        # Remove rows with empty Email
        df_concat.dropna(subset='Email', inplace=True)
        update_process(1, "Processed CSV files")  # Step 2 complete

        # Save unfiltered data
        unfiltered_path = os.path.join(process_dir, f"{csn}-process_unfiltered.csv")
        df_concat.to_csv(unfiltered_path, encoding='utf-8-sig', index=False)

        # Load master files for filtering
        # df_hb = pd.read_csv(os.path.join(MASTER_BOUNCES_DIR, "Master_Hardbounces.csv"),
        #                    on_bad_lines='skip')
        df_unsubs = pd.read_csv(os.path.join(MASTER_BOUNCES_DIR, "Master_Unsubscribes.csv"))
        # df_rep = pd.read_csv(os.path.join(REPLIED_DIR, f"{csn}_replied_bouncers.csv"))
        df_pp_gl_unsubs = pd.read_csv(os.path.join(MASTER_BOUNCES_DIR, "PP_Global_Unsubscribers.csv"))
        update_process(1, "Loaded master files")  # Step 3 complete

        # Combine unsubscribers
        df_concat_unsubs = pd.concat([df_unsubs, df_pp_gl_unsubs])
        df_concat_unsubs[['Email']] = df_concat_unsubs[['Email']].applymap(
            lambda x: x.lower() if isinstance(x, str) else x)

        # Read back the unfiltered data
        df_concat = pd.read_csv(unfiltered_path, low_memory=False)

        # Filter out hard bounces, unsubscribers, and replied
        # NOTE: Hardbounces and replied bounces filtering has been commented out
        # df_hb_filtered = df_concat[~(df_concat.Email.isin(df_hb.Email))]
        # df_unsubs_filtered = df_hb_filtered[~(df_hb_filtered.Email.isin(df_concat_unsubs.Email))]
        # df = df_unsubs_filtered[~(df_unsubs_filtered.Email.isin(df_rep.Email))]

        # Only filter out unsubscribers (hardbounces and replied bounces filtering disabled)
        df = df_concat[~(df_concat.Email.isin(df_concat_unsubs.Email))]
        update_process(1, "Filtered data")  # Step 4 complete

        # Remove Article Title column if it exists
        if 'Article Title' not in df.columns:
            df['Article Title'] = 0
        df.drop(['Article Title'], axis=1, inplace=True)

        # Save deduped data
        deduped_path = os.path.join(process_dir, f"{csn}-process_deduped.csv")
        df.to_csv(deduped_path, encoding='utf-8-sig', index=False)

        # Extract only Name and Email columns
        df = pd.read_csv(deduped_path, usecols=["Name", "Email"])

        # Clean data
        df1 = df.replace(r'^\s*$', np.nan, regex=True)
        df2 = df1.fillna('Colleague')

        # Remove duplicates
        result = df2.drop_duplicates(subset='Email')

        # Save processed data
        process_path = os.path.join(process_dir, f"{csn}-process.csv")
        result.to_csv(process_path, mode='w+', encoding='utf-8-sig', index=False)

        # Read back the processed data
        df = pd.read_csv(process_path)
        print(f"Processed {len(df)} records for {csn}")

        # Clean up temporary files
        os.remove(deduped_path)
        os.remove(unfiltered_path)
        os.remove(process_path)
        os.rmdir(process_dir)

        # Complete the progress bar
        update_process(1, "Completed processing")  # Step 5 complete
        process_bar.stop()

        return df

    except Exception as e:
        print(f"Error processing conference data: {str(e)}")
        # Close the progress bar in case of error
        process_bar.stop()
        return pd.DataFrame()


def generate_subject_lines(df: pd.DataFrame, csn: str) -> pd.DataFrame:
    """
    Generate random subject lines for each record.

    Args:
        df: DataFrame with records
        csn: Conference segment name

    Returns:
        DataFrame with added subject lines
    """
    print("Generating subject lines...")

    # List of subject line templates
    subj_list = [
        'Invitation to Submit Abstract for Oral Presentation',
        'Invitation to Submit Abstracts for [CCT_CSNAME]',
        'Call for Papers: 20-Minute Oral Presentation at [CCT_CSNAME]',
        'Submit Your Abstract for [CCT_CSNAME]',
        'Oral Presentation Slots Available at [CCT_CSNAME]',
        'Join Us as a Presenter at [CCT_CSNAME]',
        'Abstract Submission for Oral Presentations at [CCT_CSNAME] is OPEN!',
        'Your Expertise Wanted: Call for 20-Minutes Oral Presentation',
        '[CCT_CSNAME]: Now Accepting Abstract Submissions for Oral Presentations',
        'Share Your Research at [CCT_CSNAME]',
        'Invitation to Submit Abstract for 20-Minute Oral Presentation',
        'Present Your Findings at [CCT_CSNAME]',
        'Call for Oral Presentation Abstracts at [CCT_CSNAME]',
        '[CCT_CSNAME]: Call for 20-Minute Oral Presentation Abstracts',
        'Call for 20-Minute Oral Presentation Abstracts Now Open!',
        'Be Part of the Program: Submit Your Abstract Now!',
        'Call for Abstracts: [CCT_CSNAME]',
        'Submit your Research Abstract for the [CCT_CSNAME]',
        'Abstract Submission Open: [CCT_CSNAME]',
        'Submit Your Abstracts for the [CCT_CSNAME]',
        'Invitation to Speak at the [CCT_CSNAME]',
        'Be Our Guest Speaker at the [CCT_CSNAME]',
        'Call for Speakers: [CCT_CSNAME]',
        'Discovering the Future of Technology at [CCT_CSNAME]',
        'The Ultimate Networking Opportunity: [CCT_CSNAME]',
        "Don't Miss Out on [CCT_CSNAME]: Exploring the Latest Trends and Technologies",
        'Join the Conversations at [CCT_CSNAME]: A Dynamic Forum for Ideas and Inspiration'
    ]

    # Replace placeholder with actual conference name
    dynamic_subj_list = [subject.replace("[CCT_CSNAME]", csn) for subject in subj_list]

    # Create a progress bar for generating subject lines
    subject_bar, update_subject = create_gradient_bar(total=1, desc="Generating subject lines")

    # Assign random subject lines
    df["Subject"] = pd.Series(
        random.choices(dynamic_subj_list, k=len(df)),
        index=df.index
    )

    # Update and close the progress bar
    update_subject(1, "Subject lines generated")
    subject_bar.stop()

    return df


def save_output_files(df: pd.DataFrame, csn: str, n_temps: int = 1, create_splits: bool = False) -> int:
    """
    Save output files, splitting the data if needed.

    Args:
        df: DataFrame with records
        csn: Conference segment name
        n_temps: Number of templates/chunks to split into
        create_splits: Whether to create splits of 10000 rows each in the splits folder

    Returns:
        int: Number of files created
    """

    try:
        # Create output directory
        output_dir = os.path.join(os.getcwd(), "output")
        os.makedirs(output_dir, exist_ok=True)

        # Define splits directory path
        splits_dir = os.path.join(output_dir, "splits")

        # Create splits directory only if we're using splits
        if create_splits:
            os.makedirs(splits_dir, exist_ok=True)

        # Get the number of rows in the dataframe
        n_rows = len(df)

        # Maximum rows per file (10,000 as per user preference)
        max_rows_per_file = 10000

        # Track total number of files created
        total_files = 0

        # Handle based on whether we're creating splits or using templates
        if create_splits:
            # Message is now handled by the main function

            # Calculate number of splits needed
            num_splits = (n_rows + max_rows_per_file - 1) // max_rows_per_file

            # Create gradient progress bar
            splits_bar, update_splits = create_gradient_bar(total=num_splits, desc="Creating splits")

            # Process each split
            for i in range(num_splits):
                start_idx = i * max_rows_per_file
                end_idx = min((i + 1) * max_rows_per_file, n_rows)

                # Extract chunk
                chunk = df.iloc[start_idx:end_idx]

                # Save to splits directory
                output_path = os.path.join(splits_dir, f"{csn}_split_{i+1:03d}.csv")
                chunk.to_csv(output_path, encoding='utf-8-sig', mode='w+', index=False)

                total_files += 1
                update_splits(1, f"Created split {i+1}/{num_splits}")

            # Stop the progress bar
            splits_bar.stop()

        else:
            # Calculate the size of each chunk based on templates
            chunk_size = n_rows // n_temps if n_temps > 0 else n_rows

            # Create gradient progress bar for templates
            template_bar, update_template = create_gradient_bar(total=n_temps, desc="Creating template files")

            # Split the dataframe into chunks and save them as separate csv files
            for i in range(n_temps):
                start = i * chunk_size
                end = (i + 1) * chunk_size if i < n_temps - 1 else n_rows
                chunk = df[start:end]

                # Save directly to output directory
                output_path = os.path.join(output_dir, f"{csn}-{i+1}_Speaker.csv")
                chunk.to_csv(output_path, encoding='utf-8-sig', mode='w+', index=False)

                total_files += 1
                update_template(1, f"Created template {i+1}/{n_temps}")

            # Stop the progress bar
            template_bar.stop()

        return total_files

    except Exception as e:
        print_status(f"Error saving output files: {str(e)}", "error")
        return 0


def get_yes_no_input(prompt: str) -> bool:
    """
    Get a yes/no input from the user.

    Args:
        prompt: The prompt to display to the user

    Returns:
        True for yes, False for no
    """
    while True:
        response = input(f"{prompt} (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            return False
        else:
            print("Please enter 'y' or 'n'.")


def main():
    """Main function to run the script."""
    try:
        # Print header
        print_status("CSV Processing Script for Conference Data", "header")

        # Get input path
        path = input("Loc: ")

        # Extract conference segment name
        csn = extract_conference_name(path)
        print_status(f"Processing conference: {csn}", "info")

        # Process unsubscribers
        print_status("Processing unsubscribers...", "info")
        process_mw_unsubs()
        process_port1_unsubs()
        process_global_unsubs(csn)

        # Process conference data
        print_status("Processing conference data...", "info")
        df = process_conference_data(path, csn)

        if not df.empty:
            # Ask if subject lines should be generated
            include_subject = get_yes_no_input("Include subject lines in the output?")

            # Generate subject lines if requested
            if include_subject:
                print_status("Including subject lines in the output...", "info")
                df = generate_subject_lines(df, csn)
            else:
                print_status("Subject lines will not be included.", "info")

            # Ask if user wants to split the output into files of 10000 rows each
            create_splits = get_yes_no_input("Do you want to split the output into files of 10000 rows each?")

            # If user doesn't want splits, check if splits directory exists and remove it
            if not create_splits:
                splits_dir = os.path.join(os.getcwd(), "output", "splits")
                if os.path.exists(splits_dir):
                    try:
                        # Check if directory is empty
                        if not os.listdir(splits_dir):
                            os.rmdir(splits_dir)
                            print_status(f"Removed empty splits directory: {splits_dir}", "info")
                        else:
                            # Remove all files in the directory
                            for file in os.listdir(splits_dir):
                                file_path = os.path.join(splits_dir, file)
                                if os.path.isfile(file_path):
                                    os.remove(file_path)
                            # Then remove the directory
                            os.rmdir(splits_dir)
                            print_status(f"Removed splits directory and its contents: {splits_dir}", "info")
                    except Exception as e:
                        print_status(f"Could not remove splits directory: {str(e)}", "warning")

            # Ask for number of templates/chunks only if not creating splits
            n_temps = 1
            if not create_splits:
                while True:
                    try:
                        n_temps_input = input("Enter number of templates/chunks (default: 1): ").strip()
                        if not n_temps_input:  # If empty, use default
                            n_temps = 1
                            break
                        n_temps = int(n_temps_input)
                        if n_temps <= 0:
                            print_status("Please enter a positive number.", "warning")
                        else:
                            break
                    except ValueError:
                        print_status("Please enter a valid number.", "warning")

            # Save output files
            print_status("Saving output files...", "info")
            total_files = save_output_files(df, csn, n_temps, create_splits)

            if total_files > 0:
                file_type = "split" if create_splits else "template"
                print_status(f"Successfully saved {len(df)} records to {total_files} {file_type} files", "success")
                print_status(f"Total records processed: {len(df)}", "success")

        print_status("Processing completed successfully!", "success")

    except Exception as e:
        print_status(f"Error: {str(e)}", "error")
        print_status("Process terminated with errors.", "error")


if __name__ == "__main__":
    main()

